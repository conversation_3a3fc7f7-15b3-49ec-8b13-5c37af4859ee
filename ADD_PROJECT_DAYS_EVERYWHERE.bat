@echo off
echo ========================================
echo      إضافة عدد الأيام في جميع جداول المشاريع
echo ========================================
echo.

echo 🔍 البحث عن الملفات التي تحتوي على جداول المشاريع...

echo.
echo 📋 الملفات التي تم فحصها:
echo ✅ Views\ReportView.xaml - يحتوي على ProjectDays بالفعل
echo ✅ Views\ExcelPreviewWindow.xaml - يحتوي على ProjectDays بالفعل  
echo ✅ Views\ProjectsTableTemplate.xaml - تم إنشاؤه مع ProjectDays

echo.
echo 🔧 بناء المشروع...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo.
    echo 🚀 تشغيل النظام للاختبار...
    echo.
    echo 📋 خطوات الاختبار:
    echo 1. اذهب إلى قسم التقارير
    echo 2. اختر زيارة ميدانية
    echo 3. ابحث عن جدول المشاريع
    echo 4. تحقق من وجود عمود "عدد الأيام"
    echo 5. اضغط على زر "🔍 تشخيص عدد الأيام" للحصول على تفاصيل
    echo.
    echo 💡 النتائج المتوقعة:
    echo    - يجب أن يظهر عدد الأيام لكل مشروع
    echo    - إذا كان العدد 0: سيظهر "غير محدد"
    echo    - إذا كان العدد أكبر من 0: سيظهر الرقم
    echo.
    echo 📝 ملاحظة:
    echo إذا لم تجد الجدول المطلوب، تحقق من:
    echo - نافذة الطباعة (Print Preview)
    echo - نافذة التقارير المهنية
    echo - نافذة معاينة Excel
    echo.
    pause
    
    start bin\Debug\net9.0-windows\SFDSystem.exe
) else (
    echo ❌ فشل في بناء المشروع
    echo تحقق من الأخطاء أعلاه
    pause
)

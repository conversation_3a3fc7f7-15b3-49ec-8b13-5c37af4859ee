@echo off
echo ========================================
echo      اختبار إصلاح عرض عدد أيام المشاريع
echo ========================================
echo.

echo 🔍 فحص الملفات المضافة...
if exist "Converters\ProjectDaysConverter.cs" (
    echo ✅ تم العثور على ProjectDaysConverter.cs
) else (
    echo ❌ لم يتم العثور على ProjectDaysConverter.cs
)

if exist "Converters\ProjectDaysUnitConverter.cs" (
    echo ✅ تم العثور على ProjectDaysUnitConverter.cs
) else (
    echo ❌ لم يتم العثور على ProjectDaysUnitConverter.cs
)

echo.
echo 🔧 بناء المشروع...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo.
    echo 🚀 تشغيل النظام للاختبار...
    echo.
    echo 📋 خطوات الاختبار:
    echo 1. اذهب إلى قسم التقارير
    echo 2. اختر زيارة ميدانية
    echo 3. تحقق من عمود "عدد الأيام" في جدول المشاريع
    echo 4. اضغط على زر "🔍 تشخيص عدد الأيام" للحصول على تفاصيل
    echo.
    echo 💡 النتائج المتوقعة:
    echo    - إذا كان عدد الأيام أكبر من 0: سيظهر الرقم + "يوم"
    echo    - إذا كان عدد الأيام = 0: سيظهر "غير محدد"
    echo.
    pause
    
    start bin\Debug\net9.0-windows\SFDSystem.exe
) else (
    echo ❌ فشل في بناء المشروع
    echo تحقق من الأخطاء أعلاه
    pause
)

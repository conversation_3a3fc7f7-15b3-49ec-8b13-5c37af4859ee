using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public ReportView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new DriverManagementSystem.Views.ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// توثيق الرسائل النصية
        /// </summary>
        private void MessageDocumentationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة توثيق الرسائل مع الزيارة المحددة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow(selectedVisit);
                    messageWindow.ShowDialog();
                }
                else
                {
                    // فتح نافذة توثيق الرسائل العامة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow();
                    messageWindow.ShowDialog();
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة توثيق الرسائل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التكليف
        /// </summary>
        private void AssignmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة التكليف المبسطة مع الزيارة المحددة
                    var assignmentWindow = new DriverManagementSystem.Views.SimpleAssignmentWindow(selectedVisit);
                    assignmentWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار زيارة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التكليف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث عروض الأسعار من نافذة الرسائل المهنية
        /// </summary>
        private async void RefreshOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReportViewModel viewModel && viewModel.SelectedVisit != null)
                {
                    // إعادة تحميل البيانات للزيارة المحددة
                    await viewModel.LoadDriversAndPricesData(viewModel.SelectedVisit);

                    MessageBox.Show($"تم تحديث عروض الأسعار\n\nعدد العروض المحملة: {viewModel.ReportData.PriceOffers.Count}",
                                  "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا توجد زيارة ميدانية محددة للتحديث", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العروض: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تشخيص عدد الأيام في المشاريع
        /// </summary>
        private void DiagnoseProjectDays_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReportViewModel viewModel)
                {
                    var diagnosticInfo = "🔍 تشخيص عدد الأيام في المشاريع:\n\n";

                    if (viewModel.SelectedVisit != null)
                    {
                        diagnosticInfo += $"📋 الزيارة المحددة: {viewModel.SelectedVisit.VisitNumber}\n";
                        diagnosticInfo += $"📅 عدد الأيام الإجمالي: {viewModel.SelectedVisit.DaysCount}\n";
                        diagnosticInfo += $"📊 مجموع أيام المشاريع: {viewModel.SelectedVisit.TotalProjectDays}\n\n";

                        if (viewModel.SelectedVisit.Projects?.Any() == true)
                        {
                            diagnosticInfo += $"📁 عدد المشاريع في الزيارة: {viewModel.SelectedVisit.Projects.Count}\n\n";

                            for (int i = 0; i < viewModel.SelectedVisit.Projects.Count; i++)
                            {
                                var project = viewModel.SelectedVisit.Projects[i];
                                diagnosticInfo += $"مشروع {i + 1}:\n";
                                diagnosticInfo += $"  - الرقم: {project.ProjectNumber}\n";
                                diagnosticInfo += $"  - الاسم: {project.ProjectName}\n";
                                diagnosticInfo += $"  - عدد الأيام: {project.ProjectDays}\n";
                                diagnosticInfo += $"  - ID: {project.Id}\n\n";
                            }
                        }
                        else
                        {
                            diagnosticInfo += "⚠️ لا توجد مشاريع في الزيارة المحددة\n";
                        }
                    }
                    else
                    {
                        diagnosticInfo += "⚠️ لا توجد زيارة محددة\n";
                    }

                    if (viewModel.ReportData?.Projects?.Any() == true)
                    {
                        diagnosticInfo += $"\n📊 عدد المشاريع في التقرير: {viewModel.ReportData.Projects.Count}\n\n";

                        for (int i = 0; i < viewModel.ReportData.Projects.Count; i++)
                        {
                            var reportProject = viewModel.ReportData.Projects[i];
                            diagnosticInfo += $"مشروع التقرير {i + 1}:\n";
                            diagnosticInfo += $"  - الرقم: {reportProject.ProjectNumber}\n";
                            diagnosticInfo += $"  - الاسم: {reportProject.ProjectName}\n";
                            diagnosticInfo += $"  - عدد الأيام: {reportProject.ProjectDays}\n\n";
                        }
                    }
                    else
                    {
                        diagnosticInfo += "\n⚠️ لا توجد مشاريع في بيانات التقرير\n";
                    }

                    MessageBox.Show(diagnosticInfo, "تشخيص عدد الأيام", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا يمكن الوصول إلى بيانات التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشخيص: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}

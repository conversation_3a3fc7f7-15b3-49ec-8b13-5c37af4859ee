# إصلاح مشكلة عرض عدد أيام المشاريع في التقرير

## المشكلة 🚨

عمود "عدد الأيام" في جدول المشاريع لا يظهر البيانات بشكل صحيح:
- عندما تكون القيمة 0، يظهر 0 بدلاً من "غير محدد"
- لا يوجد تمييز بين القيم الحقيقية والقيم غير المحددة

## الحل ✅

### 1. إنشاء Converters للعرض المناسب

#### أ. ProjectDaysConverter
```csharp
// يحول القيمة 0 إلى "غير محدد"
// يعرض الأرقام الحقيقية كما هي
public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
{
    if (value is int days)
    {
        if (days <= 0)
        {
            return "غير محدد";
        }
        return days.ToString();
    }
    
    return "غير محدد";
}
```

#### ب. ProjectDaysUnitConverter
```csharp
// يظهر "يوم" فقط عندما تكون هناك قيمة حقيقية
public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
{
    if (value is int days && days > 0)
    {
        return "يوم";
    }
    
    return string.Empty; // لا تظهر "يوم" إذا كانت القيمة 0
}
```

### 2. تطبيق الـ Converters في XAML

```xml
<!-- إضافة الـ Converters إلى الموارد -->
<converters:ProjectDaysConverter x:Key="ProjectDaysConverter"/>
<converters:ProjectDaysUnitConverter x:Key="ProjectDaysUnitConverter"/>

<!-- تطبيق الـ Converters على العرض -->
<TextBlock Text="{Binding ProjectDays, Converter={StaticResource ProjectDaysConverter}}"
           Foreground="White" FontWeight="Bold" FontSize="18"/>
<TextBlock Text="{Binding ProjectDays, Converter={StaticResource ProjectDaysUnitConverter}}"
           Foreground="White" FontSize="12"/>
```

### 3. إضافة زر التشخيص

تم إضافة زر "🔍 تشخيص عدد الأيام" في `ReportView.xaml.cs` لمساعدة في تشخيص المشاكل:

```csharp
private void DiagnoseProjectDays_Click(object sender, RoutedEventArgs e)
{
    // يعرض تفاصيل شاملة عن:
    // - بيانات الزيارة المحددة
    // - عدد المشاريع وتفاصيلها
    // - قيم ProjectDays لكل مشروع
    // - حالة بيانات التقرير
}
```

## النتيجة 🎉

### قبل الإصلاح:
- ❌ عدد الأيام يظهر 0 دائماً
- ❌ لا يوجد تمييز بين القيم المحددة وغير المحددة
- ❌ صعوبة في تشخيص المشكلة

### بعد الإصلاح:
- ✅ القيم الحقيقية تظهر مع "يوم" (مثل: "5 يوم")
- ✅ القيم غير المحددة تظهر "غير محدد"
- ✅ عرض واضح ومفهوم للمستخدم
- ✅ زر تشخيص لحل المشاكل

## كيفية الاختبار 🧪

1. **شغل النظام**
2. **اذهب إلى قسم التقارير**
3. **اختر زيارة ميدانية**
4. **تحقق من جدول المشاريع:**
   - إذا كان المشروع له أيام محددة: سيظهر "X يوم"
   - إذا لم يكن محدد: سيظهر "غير محدد"
5. **اضغط على زر "🔍 تشخيص عدد الأيام"** للحصول على تفاصيل

## الملفات المعدلة 📁

1. **Converters/ProjectDaysConverter.cs** - جديد
2. **Converters/ProjectDaysUnitConverter.cs** - جديد  
3. **Views/ReportView.xaml** - تم تعديله لاستخدام الـ Converters
4. **Views/ReportView.xaml.cs** - تم إضافة زر التشخيص
5. **ViewModels/ReportViewModel.cs** - تحسين معالجة البيانات

## ملاحظات مهمة 📝

- الحل يحافظ على البيانات الأصلية في قاعدة البيانات
- التغيير فقط في طريقة العرض للمستخدم
- يمكن تخصيص النصوص بسهولة من خلال الـ Converters
- الحل متوافق مع جميع أنواع البيانات الموجودة

## استكشاف الأخطاء 🔧

إذا لم تظهر البيانات بشكل صحيح:

1. **استخدم زر التشخيص** للحصول على تفاصيل البيانات
2. **تحقق من Debug Output** لرؤية رسائل التشخيص
3. **تأكد من وجود مشاريع في الزيارة المحددة**
4. **تحقق من قيم ProjectDays في قاعدة البيانات**

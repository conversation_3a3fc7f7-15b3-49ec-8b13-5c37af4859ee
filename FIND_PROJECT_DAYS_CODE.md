# البحث عن كود عرض عدد أيام المشاريع

## الكود المطلوب إضافته:
```xml
<TextBlock Text="{Binding ProjectDays}" FontSize="10" FontWeight="Bold" Foreground="#2C3E50"/>
```

## الكود الأصلي الذي يجب تعديله:
```xml
<Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="8,5">
    <StackPanel>
        <TextBlock Text="{Binding ProjectNumber}" FontWeight="Bold" FontSize="10" Foreground="#2C3E50"/>
        <TextBlock Text="{Binding ProjectName}" FontSize="10" Foreground="#666666" TextWrapping="Wrap"/>
    </StackPanel>
</Border>
```

## الكود بعد التعديل:
```xml
<Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="8,5">
    <StackPanel>
        <TextBlock Text="{Binding ProjectNumber}" FontWeight="Bold" FontSize="10" Foreground="#2C3E50"/>
        <TextBlock Text="{Binding ProjectName}" FontSize="10" Foreground="#666666" TextWrapping="Wrap"/>
        <TextBlock Text="{Binding ProjectDays}" FontSize="10" FontWeight="Bold" Foreground="#2C3E50"/>
    </StackPanel>
</Border>
```

## الملفات المحتملة:
1. Views\ReportView.xaml ✅ (تم فحصه - لا يحتوي على الكود المطلوب)
2. Views\PrintPreviewWindow.xaml ✅ (تم فحصه - لا يحتوي على الكود المطلوب)
3. Views\ProfessionalPrintPreviewWindow.xaml ✅ (تم فحصه - لا يحتوي على الكود المطلوب)
4. Views\ReportWindow.xaml ✅ (تم فحصه - لا يحتوي على الكود المطلوب)
5. Views\DropDataView.xaml ✅ (تم فحصه - لا يحتوي على الكود المطلوب)

## الحلول البديلة:

### 1. إنشاء قالب جديد (تم إنشاؤه):
- ملف: `Views\ProjectsTableTemplate.xaml`
- يحتوي على الكود المطلوب مع إضافة ProjectDays

### 2. البحث في ملفات C# التي تنشئ XAML ديناميكياً:
- Views\PrintPreviewWindow.xaml.cs
- Views\ProfessionalPrintPreviewWindow.xaml.cs
- Views\ReportWindow.xaml.cs
- Services\ReportViewPrintService.cs

### 3. البحث في ملفات أخرى:
- Views\ExcelPreviewWindow.xaml (يحتوي على ProjectDays بالفعل)
- Views\FieldVisitsLogView.xaml (يحتوي على مشاريع)

## التوصية:
إذا لم يتم العثور على الكود الأصلي، يمكن:
1. استخدام القالب الجديد `ProjectsTableTemplate.xaml`
2. أو إضافة الكود مباشرة في الملف الذي يحتوي على الجدول المطلوب
3. أو إنشاء Converter لعرض ProjectDays بشكل مناسب

## ملاحظة:
بناءً على الصورة المرسلة، يبدو أن الكود موجود في ملف XAML يحتوي على جدول المشاريع.
قد يكون الكود في ملف لم يتم فحصه بعد أو في قسم مختلف من الملفات المفحوصة.
